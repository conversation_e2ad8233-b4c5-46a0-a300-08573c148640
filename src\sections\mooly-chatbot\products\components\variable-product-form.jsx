'use client';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';

import { fetchData } from 'src/actions/mooly-chatbot/supabase-utils';

import { Field } from 'src/components/hook-form';

import ProductType from '../product-type';
import ProductMedia from '../product-media';
import ProductVariants from '../product-variants';
import ProductBasicInfo from '../product-basic-info';

// ----------------------------------------------------------------------

function useCategories() {
  const [categories, setCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const result = await fetchData('product_categories', {
          orderBy: 'name',
          ascending: true,
        });

        if (result.success) {
          setCategories(result.data || []);
        } else {
          setError('Không thể tải danh mục sản phẩm');
          console.error('Failed to fetch categories:', result.error);
        }
      } catch (err) {
        setError('Lỗi khi tải danh mục sản phẩm');
        console.error('Error fetching categories:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return { categories, isLoading, error };
}

export default function VariableProductForm({ watch, setValue }) {
  const { categories, isLoading, error } = useCategories();

  return (
    <Stack spacing={3}>
      {/* Loại sản phẩm */}
      {/* <ProductType /> */}

      {/* Thông tin cơ bản */}
      <ProductBasicInfo />

      {/* Hình ảnh sản phẩm */}
      <ProductMedia watch={watch} setValue={setValue} />

      {/* Giá cơ bản */}
      <Card sx={{ border: 2, borderColor: 'warning.main', boxShadow: 2 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, color: 'warning.main', fontWeight: 600 }}>
            Giá cơ bản
            <Typography component="span" variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
              (Sẽ được ghi đè bởi giá biến thể)
            </Typography>
          </Typography>
          
          <Stack spacing={2}>
            {/* Giá bán cơ bản */}
            <Field.Text
              name="price"
              label="Giá bán cơ bản"
              type="number"
              required
              placeholder="0"
              InputProps={{
                endAdornment: 'VNĐ',
              }}
              helperText="Giá này sẽ được sử dụng làm giá mặc định cho các biến thể"
              inputProps={{
                min: 0,
                step: 1000,
              }}
            />
          </Stack>
        </CardContent>
      </Card>

      {/* Biến thể sản phẩm */}
      <ProductVariants watch={watch} setValue={setValue} />
    </Stack>
  );
}
