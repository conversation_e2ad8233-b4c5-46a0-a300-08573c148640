'use client';

import { useMemo, useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';
import { checkSKUExists } from 'src/actions/mooly-chatbot/product-api';

import { getSchemaByType, defaultValues } from '../product-schema';

/**
 * Custom hook để quản lý form sản phẩm với validation tối ưu
 * @param {Object} options - T<PERSON><PERSON> chọn cấu hình
 * @param {Object} options.currentProduct - Sản phẩm hiện tại (cho chế độ edit)
 * @param {string} options.productId - ID sản phẩm (cho chế độ edit)
 * @param {boolean} options.isEditMode - Chế độ edit hay tạo mới
 * @returns {Object} - Form methods và utilities
 */
export function useProductForm({ currentProduct = null, productId = null, isEditMode = false, initialProductType = 'simple' } = {}) {
  // Xử lý dữ liệu currentProduct an toàn
  const safeCurrentProduct = useMemo(() => {
    if (!currentProduct) return null;

    const normalizeAttributes = (attributes) => {
      if (Array.isArray(attributes)) return attributes;
      if (attributes && typeof attributes === 'object') {
        return Object.entries(attributes).map(([name, values]) => ({
          name,
          values: Array.isArray(values) ? values : [],
        }));
      }
      return [];
    };

    return {
      ...currentProduct,
      tags: Array.isArray(currentProduct.tags) ? currentProduct.tags : [],
      gender: Array.isArray(currentProduct.gender) ? currentProduct.gender : [],
      images: Array.isArray(currentProduct.images) ? currentProduct.images : [],
      variants: Array.isArray(currentProduct.variants) ? currentProduct.variants : [],
      saleLabel: currentProduct.saleLabel || { enabled: false, content: '' },
      newLabel: currentProduct.newLabel || { enabled: false, content: '' },
      attributes: normalizeAttributes(currentProduct.attributes),
    };
  }, [currentProduct]);

  // State cho loại sản phẩm hiện tại
  const [currentProductType, setCurrentProductType] = useState(
    safeCurrentProduct?.type || initialProductType || defaultValues.type
  );

  // Tạo schema dựa trên loại sản phẩm
  const currentSchema = useMemo(() => getSchemaByType(currentProductType), [currentProductType]);

  // Khởi tạo form với React Hook Form
  const methods = useForm({
    mode: 'onBlur', // Validate khi blur để tối ưu performance
    resolver: zodResolver(currentSchema),
    defaultValues: {
      ...defaultValues,
      type: initialProductType || defaultValues.type,
    },
    values: safeCurrentProduct,
  });

  const {
    watch,
    setValue,
    getValues,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors, isValid },
  } = methods;

  // Watch product type để cập nhật schema
  const productType = watch('type');

  useEffect(() => {
    if (productType && productType !== currentProductType) {
      setCurrentProductType(productType);
    }
  }, [productType, currentProductType]);

  // Validate SKU - cho phép SKU trống để tự động tạo
  const validateSKU = useCallback(async (sku) => {
    // Nếu SKU trống, không cần validate (sẽ tự động tạo)
    if (!sku || sku.trim() === '') {
      return; // Cho phép SKU trống
    }

    try {
      const exists = await checkSKUExists(sku, isEditMode ? productId : null);
      if (exists) {
        throw new Error('SKU đã tồn tại. Vui lòng sử dụng SKU khác.');
      }
    } catch (checkError) {
      console.error('Error checking SKU:', checkError);
      throw new Error('Không thể kiểm tra SKU. Vui lòng thử lại.');
    }
  }, [isEditMode, productId]);

  // Chuẩn bị dữ liệu trước khi submit
  const prepareSubmitData = useCallback((data) => {
    const productData = { ...data };

    // Đảm bảo dữ liệu đúng định dạng
    productData.price = Number(data.price) || 0;
    productData.stockQuantity = Number(data.stockQuantity) || 0;

    // Xử lý variants cho sản phẩm có biến thể
    if (productType !== PRODUCT_TYPES.SIMPLE) {
      productData.variants = data.variants || [];
    }

    // Đảm bảo các trường bắt buộc có giá trị
    productData.images = data.images || [];
    productData.isActive = data.isActive !== false;
    productData.trackInventory = data.trackInventory !== false;

    // Tối ưu dữ liệu cho sản phẩm simple
    if (productType === PRODUCT_TYPES.SIMPLE) {
      // Đặt giá trị mặc định cho các trường không cần thiết
      productData.attributes = [];
      productData.variants = [];
      productData.tags = data.tags || [];
      productData.gender = [];
      productData.saleLabel = { enabled: false, content: '' };
      productData.newLabel = { enabled: false, content: '' };
      productData.metaKeywords = [];
      productData.isFeatured = false;
      productData.taxes = null;
      productData.includeTaxes = false;
      productData.costPrice = null;
      productData.salePrice = null;
    }

    return productData;
  }, [productType]);

  // Submit handler với validation tự động
  const createSubmitHandler = useCallback((onSubmitSuccess, onSubmitError) => handleSubmit(async (data) => {
      try {
        // Validate SKU trước khi submit
        await validateSKU(data.sku);

        // Chuẩn bị dữ liệu
        const productData = prepareSubmitData(data);

        // Gọi callback success
        await onSubmitSuccess(productData);
      } catch (error) {
        // Gọi callback error
        onSubmitError(error);
      }
    }), [handleSubmit, validateSKU, prepareSubmitData]);

  // Utilities
  const isSimpleProduct = productType === PRODUCT_TYPES.SIMPLE;
  const hasErrors = Object.keys(errors).length > 0;

  // Auto-generate slug từ name
  const generateSlug = useCallback((name) => {
    if (!name) return '';
    
    return name
      .toLowerCase()
      .trim()
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
      .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
      .replace(/[ìíịỉĩ]/g, 'i')
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
      .replace(/[ùúụủũưừứựửữ]/g, 'u')
      .replace(/[ỳýỵỷỹ]/g, 'y')
      .replace(/đ/g, 'd')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }, []);

  // Auto-generate slug khi name thay đổi
  const productName = watch('name');
  useEffect(() => {
    if (productName && !isEditMode) {
      const slug = generateSlug(productName);
      setValue('slug', slug);
    }
  }, [productName, isEditMode, generateSlug, setValue]);

  return {
    // Form methods
    ...methods,
    
    // Custom handlers
    createSubmitHandler,
    
    // Utilities
    isSimpleProduct,
    hasErrors,
    isValid,
    currentProductType,
    
    // States
    isSubmitting,
    errors,
    
    // Helpers
    generateSlug,
    prepareSubmitData,
    validateSKU,
  };
}
